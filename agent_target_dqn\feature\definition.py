#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import numpy as np
from kaiwu_agent.utils.common_func import attached, create_cls
from agent_target_dqn.conf.conf import Config

# The create_cls function is used to dynamically create a class. The first parameter of the function is the type name,
# and the remaining parameters are the attributes of the class, which should have a default value of None.
# create_cls函数用于动态创建一个类，函数第一个参数为类型名称，剩余参数为类的属性，属性默认值应设为None
ObsData = create_cls(
    "ObsData",
    feature=None,
    maps_tensor=None,
    legal_act=None,
)


ActData = create_cls(
    "ActData",
    move_dir=None,
    use_talent=None,
)


SampleData = create_cls(
    "SampleData",
    obs=None,
    _obs=None,
    maps_tensor=None,
    _maps_tensor=None,
    obs_legal=None,
    _obs_legal=None,
    act=None,
    rew=None,
    ret=None,
    done=None,
)

RelativeDistance = {
    "RELATIVE_DISTANCE_NONE": 0,
    "VerySmall": 1,
    "Small": 2,
    "Medium": 3,
    "Large": 4,
    "VeryLarge": 5,
}


RelativeDirection = {
    "East": 1,
    "NorthEast": 2,
    "North": 3,
    "NorthWest": 4,
    "West": 5,
    "SouthWest": 6,
    "South": 7,
    "SouthEast": 8,
}

DirectionAngles = {
    1: 0,
    2: 45,
    3: 90,
    4: 135,
    5: 180,
    6: 225,
    7: 270,
    8: 315,
}


def reward_process(cur_obs,
                    cur_extra_info,
                    prev_obs,
                    prev_extra_info,
                    local_memory_map,
                    last_target,
                    target,
                    ):

    # step reward
    # 步数奖励
    step_reward = -0.05
    memory_reward = 0.0
    if local_memory_map[5,5] >= 0.3:
        memory_reward = -local_memory_map[5,5]

    stop_reward = 0.0
    talent_reward = 0.0

    buff_reward = 0
    dist_reward = 0
    if target is not None and prev_obs is not None:
        if prev_obs['frame_state']['heroes'][0]['pos'] == cur_obs['frame_state']['heroes'][0]['pos']:
            stop_reward = -0.5

        if cur_obs['score_info']['treasure_collected_count'] > prev_obs['score_info']['treasure_collected_count']:
            return [5.0]
        if cur_obs['score_info']['buff_count'] > prev_obs['score_info']['buff_count']:
            buff_reward = 0.5 ** cur_obs['score_info']['buff_count']

        if target['pos'][0] != None:
            cur_pos = cur_obs['frame_state']['heroes'][0]['pos']
            prev_pos = prev_obs['frame_state']['heroes'][0]['pos']
            prev_dist = abs(prev_pos['x'] - target['pos'][0]) + abs(prev_pos['z'] - target['pos'][1])
            cur_dist = abs(cur_pos['x'] - target['pos'][0]) + abs(cur_pos['z'] - target['pos'][1])
            dist_reward = prev_dist - cur_dist

        if cur_obs['score_info']['talent_count'] > prev_obs['score_info']['talent_count']:
            talent_reward = -0.5


    #print(dist_reward)
    return [step_reward + memory_reward + stop_reward + talent_reward + buff_reward + dist_reward]


@attached
def sample_process(list_game_data):
    return [SampleData(**i.__dict__) for i in list_game_data]


@attached
def SampleData2NumpyData(g_data):
    # 處理 maps_tensor，如果存在則展平，否則創建零向量
    maps_tensor_flat = np.array(g_data.maps_tensor, dtype=np.float32).flatten() if g_data.maps_tensor is not None else np.zeros(5*11*11, dtype=np.float32)
    _maps_tensor_flat = np.array(g_data._maps_tensor, dtype=np.float32).flatten() if g_data._maps_tensor is not None else np.zeros(5*11*11, dtype=np.float32)

    return np.hstack(
        (
            np.array(g_data.obs, dtype=np.float32),
            np.array(g_data._obs, dtype=np.float32),
            maps_tensor_flat,
            _maps_tensor_flat,
            np.array(g_data.obs_legal, dtype=np.float32),
            np.array(g_data._obs_legal, dtype=np.float32),
            np.array(g_data.act, dtype=np.float32),
            np.array(g_data.rew, dtype=np.float32),
            np.array(g_data.ret, dtype=np.float32),
            np.array(g_data.done, dtype=np.float32),
        )
    )


@attached
def NumpyData2SampleData(s_data):
    obs_data_size = Config.DIM_OF_OBSERVATION
    maps_tensor_size = 5 * 11 * 11  # 5×11×11 = 605
    legal_data_size = Config.DIM_OF_ACTION_DIRECTION

    # 計算各部分的索引
    obs_end = obs_data_size
    _obs_end = obs_end + obs_data_size
    maps_tensor_end = _obs_end + maps_tensor_size
    _maps_tensor_end = maps_tensor_end + maps_tensor_size
    obs_legal_end = _maps_tensor_end + legal_data_size
    _obs_legal_end = obs_legal_end + legal_data_size

    return SampleData(
        obs=s_data[:obs_end],
        _obs=s_data[obs_end:_obs_end],
        maps_tensor=s_data[_obs_end:maps_tensor_end].reshape(5, 11, 11),
        _maps_tensor=s_data[maps_tensor_end:_maps_tensor_end].reshape(5, 11, 11),
        obs_legal=s_data[_maps_tensor_end:obs_legal_end],
        _obs_legal=s_data[obs_legal_end:_obs_legal_end],
        act=s_data[-4],
        rew=s_data[-3],
        ret=s_data[-2],
        done=s_data[-1],
    )
