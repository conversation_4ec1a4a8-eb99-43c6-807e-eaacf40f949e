#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch
import numpy as np
from torch import nn
import torch.nn.functional as F
from typing import List
from agent_target_dqn.conf.conf import Config

import sys
import os

if os.path.basename(sys.argv[0]) == "learner.py":
    import torch

    torch.set_num_interop_threads(2)
    torch.set_num_threads(2)
else:
    import torch

    torch.set_num_interop_threads(4)
    torch.set_num_threads(4)


class Model(nn.Module):
    def __init__(self, state_shape, action_shape=0, softmax=False):
        super().__init__()
        # feature configure parameter
        # 特征配置参数
        self.feature_len = Config.DIM_OF_OBSERVATION

        # ---------- CNN 子網路處理 maps_tensor (5×11×11) ----------
        self.cnn_encoder = nn.Sequential(
            # 第一層卷積：5通道 -> 16通道，3x3卷積核
            nn.Conv2d(5, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm2d(16),

            # 第二層卷積：16通道 -> 32通道，3x3卷積核
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm2d(32),

            # 第三層卷積：32通道 -> 64通道，3x3卷積核
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm2d(64),

            # 全局平均池化，將 11x11 壓縮為 1x1
            nn.AdaptiveAvgPool2d((1, 1))
        )

        # CNN 輸出維度：64 (通道數)
        self.cnn_output_dim = 64

        # 合併特徵維度：原始特徵 + CNN特徵
        self.combined_dim = self.feature_len + self.cnn_output_dim

        # Q network
        # Q 网络
        self.q_mlp = MLP([self.combined_dim, 256, 128, action_shape], "q_mlp")

    # Forward inference
    # 前向推理
    def forward(self, feature, maps_tensor):
        # ------------- CNN 處理 maps_tensor (5×11×11) -------------
        # maps_tensor: (B, 5, 11, 11) -> (B, 64, 1, 1) -> (B, 64)
        cnn_features = self.cnn_encoder(maps_tensor).squeeze(-1).squeeze(-1)

        # ------------- 特徵融合 -------------
        # 將原始特徵與 CNN 特徵 concat
        combined_features = torch.cat([feature, cnn_features], dim=1)  # (B, feature_len + 64)

        # Action and value processing
        logits = self.q_mlp(combined_features)
        return logits


def make_fc_layer(in_features: int, out_features: int):
    # Wrapper function to create and initialize a linear layer
    # 创建并初始化一个线性层
    fc_layer = nn.Linear(in_features, out_features)

    # initialize weight and bias
    # 初始化权重及偏移量
    nn.init.orthogonal(fc_layer.weight)
    nn.init.zeros_(fc_layer.bias)

    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        # Create a MLP object
        # 创建一个 MLP 对象
        super().__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module("{0}_fc{1}".format(name, i + 1), fc_layer)
            # no relu for the last fc layer of the mlp unless required
            # 除非有需要，否则 mlp 的最后一个 fc 层不使用 relu
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module("{0}_non_linear{1}".format(name, i + 1), non_linearity())

    def forward(self, data):
        return self.fc_layers(data)
